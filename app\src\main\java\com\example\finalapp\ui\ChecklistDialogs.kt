package com.example.finalapp.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import kotlinx.coroutines.launch
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.finalapp.viewmodel.JobViewModel
import com.example.finalapp.viewmodel.CounterViewModel
import com.example.finalapp.model.Survey

data class ChecklistItem(
    val id: String,
    val text: String,
    val isChecked: Boolean = false,
    val isSectionTitle: Boolean = false // Add flag for section titles
)

/**
 * Helper function to check if checklist can be completed.
 * Returns true if all items are checked OR only the 4th non-section item is checked (development bypass).
 */
private fun canCompleteChecklist(checklistItems: List<ChecklistItem>): Boolean {
    val nonSectionItems = checklistItems.filter { !it.isSectionTitle }
    val allChecked = nonSectionItems.all { it.isChecked }

    // Development bypass: allow completion if only the 4th checkbox is selected
    val fourthItemOnlyChecked = if (nonSectionItems.size >= 4) {
        val fourthItem = nonSectionItems[3] // 0-indexed, so 4th item is at index 3
        fourthItem.isChecked && nonSectionItems.filterIndexed { index, _ -> index != 3 }.all { !it.isChecked }
    } else {
        false
    }

    return allChecked || fourthItemOnlyChecked
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreDriveChecklistDialog(
    onDismiss: () -> Unit,
    onComplete: () -> Unit,
    jobViewModel: JobViewModel,
    counterViewModel: CounterViewModel,
    surveyForPreview: Survey? = null // Add survey parameter for preview during predrive checklist
) {
    var checklistItems by remember {
        mutableStateOf(
            listOf(
                ChecklistItem("master_calendar", "Check master calendar", isSectionTitle = true),
                ChecklistItem("correct_drone", "Confirm you have the correct drone"),
                ChecklistItem("job_type", "Confirm type of job"),
                ChecklistItem("equipment", "Check equipment", isSectionTitle = true),
                ChecklistItem("drone_labels", "Drone and controller labels match that on the case"),
                ChecklistItem("batteries", "4 fully charged batteries in the case"),
                ChecklistItem("beacon_headlamp", "Charged beacon light + headlamp"),
                ChecklistItem("propellers", "Set of propellers and extras"),
                ChecklistItem("gimbal_cover", "Gimbal cover"),
                ChecklistItem("microfiber", "Microfiber cloth"),
                ChecklistItem("turn_on_drone", "Turn on drone and controller", isSectionTitle = true),
                ChecklistItem("controller_charged", "Controller is fully charged"),
                ChecklistItem("both_updated", "Both controller and drone are updated"),
                ChecklistItem("sd_storage", "SD card inserted and has enough storage"),
                ChecklistItem("flight_paths", "Verify flight paths are loaded for job"),
                ChecklistItem("geo_zone", "IF NEEDED: Import GEO Zone unlocking certificate"),
                ChecklistItem("backpack_equipment", "Open backpack and check for all necessary equipment", isSectionTitle = true),
                ChecklistItem("hdmi_cord", "HDMI replacement cord"),
                ChecklistItem("replacement_sd", "Replacement SD card"),
                ChecklistItem("usb_usbc", "USB to USB-C replacement cord"),
                ChecklistItem("usbc_usbc", "USB-C to USB-C replacement cord"),
                ChecklistItem("drone_charger", "Drone battery charger"),
                ChecklistItem("extra_monitor", "Extra monitor"),
                ChecklistItem("battery_bank", "Portable battery bank charger"),
                ChecklistItem("sd_adapter", "SD card adapter"),
                ChecklistItem("tablet", "Tablet"),
                ChecklistItem("tablet_charger", "Tablet charger"),
                ChecklistItem("power_bank_charger", "Power bank charger"),
                ChecklistItem("extension_cord", "Extension cord"),
                ChecklistItem("power_source", "Grab correct power source for job", isSectionTitle = true),
                ChecklistItem("bluetti_charged", "IF Bluetti power bank: Fully charged"),
                ChecklistItem("generator_gas", "IF GENERATOR: Full gas can"),
                ChecklistItem("weather", "Check current weather for location of job", isSectionTitle = true),
                ChecklistItem("rain_check", "Not supposed to rain or only passing rain"),
                ChecklistItem("wind_check", "No winds over 25 mph"),
                ChecklistItem("dressed_layers", "You're dressed accordingly with layers"),
                ChecklistItem("car", "Car", isSectionTitle = true),
                ChecklistItem("car_capabilities", "Car capabilities meet that which is required"),
                ChecklistItem("folding_chair", "Folding chair in car (Optional)"),
                ChecklistItem("documents", "Documents", isSectionTitle = true),
                ChecklistItem("faa_license", "FAA license"),
                ChecklistItem("driver_license", "Driver's license"),
                ChecklistItem("company_card", "Company card"),
                ChecklistItem("getting_there", "Getting there", isSectionTitle = true),
                ChecklistItem("digital_pin", "Digital pin or coordinates to the front gate"),
                ChecklistItem("gate_codes", "Gate codes you may need"),
                ChecklistItem("contact_landowner", "Contact landowner with your ETA+15 minutes")
            )
        )
    }

    var showJobDetails by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Pre-Drive Checklist",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Row {
                        IconButton(onClick = { showJobDetails = true }) {
                            Icon(Icons.Default.Info, contentDescription = "Job Details")
                        }
                        IconButton(
                            onClick = onDismiss,
                            colors = IconButtonDefaults.iconButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = "Exit checklist")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Checklist items
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    checklistItems.forEach { item ->
                        if (item.isSectionTitle) {
                            Text(
                                text = item.text,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                        } else {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Checkbox(
                                    checked = item.isChecked,
                                    onCheckedChange = { checked ->
                                        checklistItems = checklistItems.map {
                                            if (it.id == item.id) it.copy(isChecked = checked)
                                            else it
                                        }
                                    }
                                )
                                Text(
                                    text = item.text,
                                    modifier = Modifier.weight(1f),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Complete button (enabled when all items are checked OR only 4th item is checked for development)
                Button(
                    onClick = onComplete,
                    enabled = canCompleteChecklist(checklistItems),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Complete Pre-Drive Checklist")
                }
            }
        }
    }

    // Job Details Dialog
    if (showJobDetails) {
        JobDetailsDialog(
            onDismiss = { showJobDetails = false },
            viewModel = jobViewModel,
            counterViewModel = counterViewModel,
            surveyForPreview = surveyForPreview // Pass survey for preview if available
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PreFlightChecklistDialog(
    onDismiss: () -> Unit,
    onComplete: () -> Unit,
    jobViewModel: JobViewModel,
    counterViewModel: CounterViewModel
) {
    var checklistItems by remember {
        mutableStateOf(
            listOf(
                ChecklistItem("loa_form", "Review LOA form with landowner or ranch manager", isSectionTitle = true),
                ChecklistItem("survey_goals", "Discuss their survey goals (locate a certain buck, get photos of large bucks, etc.)"),
                ChecklistItem("species_list", "Go over list of species they want to have counted as well as genders/ages"),
                ChecklistItem("certain_animals", "Ask if there are certain animals only in certain pastures"),
                ChecklistItem("dont_count", "Ask about any animals they may have but don't need counted"),
                ChecklistItem("flight_path", "Review flight path with landowner or ranch manager", isSectionTitle = true),
                ChecklistItem("separate_pastures", "Make sure that any separate pastures are correctly reflected by the paths"),
                ChecklistItem("onx_elevation", "Cross-reference OnX elevation model to find high elevation points"),
                ChecklistItem("plan_spots", "Plan out how you will get to the different spots"),
                ChecklistItem("final_checks", "Final Checks", isSectionTitle = true),
                ChecklistItem("services_notes", "Confirm services listed in notes are what they want (game maps can be added for $0.20/acre)"),
                ChecklistItem("expected_time", "Communicate with landowner the expected time survey will take"),
                ChecklistItem("after_survey", "If landowner is not going with you, then agree on what to do after survey")
            )
        )
    }

    var showJobDetails by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Pre-Flight Checklist",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Row {
                        IconButton(onClick = { showJobDetails = true }) {
                            Icon(Icons.Default.Info, contentDescription = "Job Details")
                        }
                        IconButton(
                            onClick = onDismiss,
                            colors = IconButtonDefaults.iconButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = "Exit checklist")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Checklist items
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    checklistItems.forEach { item ->
                        if (item.isSectionTitle) {
                            Text(
                                text = item.text,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                        } else {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Checkbox(
                                    checked = item.isChecked,
                                    onCheckedChange = { checked ->
                                        checklistItems = checklistItems.map {
                                            if (it.id == item.id) it.copy(isChecked = checked)
                                            else it
                                        }
                                    }
                                )
                                Text(
                                    text = item.text,
                                    modifier = Modifier.weight(1f),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Complete button (enabled when all items are checked OR only 4th item is checked for development)
                Button(
                    onClick = onComplete,
                    enabled = canCompleteChecklist(checklistItems),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Complete Pre-Flight Checklist")
                }
            }
        }
    }

    // Job Details Dialog
    if (showJobDetails) {
        JobDetailsDialog(
            onDismiss = { showJobDetails = false },
            viewModel = jobViewModel,
            counterViewModel = counterViewModel
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EquipmentSetupChecklistDialog(
    onDismiss: () -> Unit,
    onComplete: () -> Unit,
    jobViewModel: JobViewModel,
    counterViewModel: CounterViewModel
) {
    var checklistItems by remember {
        mutableStateOf(
            listOf(
                ChecklistItem("drone_preparation", "Drone Preparation", isSectionTitle = true),
                ChecklistItem("remove_drone", "Remove drone from case"),
                ChecklistItem("extend_arms", "Extend arms and attach propellers"),
                ChecklistItem("remove_gimbal", "Remove gimbal cover and place it inside case"),
                ChecklistItem("wipe_lenses", "Use microfiber cloth to gently wipe all camera lenses off"),
                ChecklistItem("inspect_drone", "Inspect drone for any damage", isSectionTitle = true),
                ChecklistItem("structure", "Structure"),
                ChecklistItem("motors", "Motors"),
                ChecklistItem("batteries", "Batteries"),
                ChecklistItem("gimbal", "Gimbal"),
                ChecklistItem("beacon_light", "Attach beacon light to drone via velcro and power on"),
                ChecklistItem("loadout_case_prep", "Load Out Case Preparation", isSectionTitle = true),
                ChecklistItem("loadout_case", "Open loadout case and plug power cord into power source"),
                ChecklistItem("plug_controller", "Plug controller in", isSectionTitle = true),
                ChecklistItem("power_cord", "Power cord (USB-C)"),
                ChecklistItem("monitor_cord", "Monitor cord (HDMI)"),
                ChecklistItem("power_source", "Power on power source"),
                ChecklistItem("flight_settings", "Flight Settings Preparation", isSectionTitle = true),
                ChecklistItem("geo_zone_unlock", "IF NECESSARY: Unlock Geo Zone"),
                ChecklistItem("correct_route", "Pull up correct route you are in position to fly"),
                ChecklistItem("current_view", "Double check that only \"Current View\" is selected for video mode"),
                ChecklistItem("camera_mode", "Set camera mode to \"Infrared\" for thermal jobs, and \"infrared\" + \"visible\" for visible camera jobs"),
                ChecklistItem("ir_mode", "Ensure IR mode is \"High-Res\""),
                ChecklistItem("begin_recording", "As soon as drone arrives at starting point, begin recording")
            )
        )
    }

    var showJobDetails by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Equipment Setup",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Row {
                        IconButton(onClick = { showJobDetails = true }) {
                            Icon(Icons.Default.Info, contentDescription = "Job Details")
                        }
                        IconButton(
                            onClick = onDismiss,
                            colors = IconButtonDefaults.iconButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = "Exit checklist")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Checklist items
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    checklistItems.forEach { item ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = item.isChecked,
                                onCheckedChange = { checked ->
                                    checklistItems = checklistItems.map {
                                        if (it.id == item.id) it.copy(isChecked = checked)
                                        else it
                                    }
                                }
                            )
                            Text(
                                text = item.text,
                                modifier = Modifier.weight(1f),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Complete button (enabled when all items are checked OR only 4th item is checked for development)
                Button(
                    onClick = onComplete,
                    enabled = canCompleteChecklist(checklistItems),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Complete Equipment Setup")
                }
            }
        }
    }

    // Job Details Dialog
    if (showJobDetails) {
        JobDetailsDialog(
            onDismiss = { showJobDetails = false },
            viewModel = jobViewModel,
            counterViewModel = counterViewModel
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostFlightChecklistDialog(
    onDismiss: () -> Unit,
    onComplete: () -> Unit,
    jobViewModel: JobViewModel,
    counterViewModel: CounterViewModel
) {
    var checklistItems by remember {
        mutableStateOf(
            listOf(
                ChecklistItem("equipment_cleanup", "Equipment Clean-Up", isSectionTitle = true),
                ChecklistItem("turn_off_power", "Turn off power source"),
                ChecklistItem("power_controller_off", "Power controller off and unplug from loadout case"),
                ChecklistItem("fold_antennas", "Fold down controller antennas and place controller securely in case"),
                ChecklistItem("remove_batteries", "Remove batteries from drone and loadout case and place back in drone case"),
                ChecklistItem("remove_propellers", "Remove propellers and place in case"),
                ChecklistItem("fold_arms", "Fold drone arms back in"),
                ChecklistItem("wipe_cameras", "Gently wipe off cameras with microfiber cloth"),
                ChecklistItem("protective_shield", "Place protective shield over gimbal camera"),
                ChecklistItem("remove_beacon", "Remove beacon light and plug into loadout case"),
                ChecklistItem("place_drone", "Place drone in case and cover with foam piece"),
                ChecklistItem("unplug_loadout", "Unplug loadout case power cord and fold back into slot"),
                ChecklistItem("fold_wires", "Fold controller wires back into respective slots in loadout case"),
                ChecklistItem("plug_headlamp", "Plug headlamp into loadout case"),
                ChecklistItem("close_loadout", "Close and latch loadout case"),
                ChecklistItem("loose_cords", "Any extra, loose cords used put in backpack"),
                ChecklistItem("proper_equipment", "Ensure the proper backpack, loadout cases, and drone case are with the correct pilot and in their vehicle"),
                ChecklistItem("post_flight_logistics", "Post-Flight Logistics", isSectionTitle = true),
                ChecklistItem("notify_landowner", "Notify landowner via text you are finished if they are not present"),
                ChecklistItem("photo_gate", "Take a photo of the locked gate behind you and send it to landowner"),
                ChecklistItem("notify_ethan", "Notify Ethan at (************* you are finished and what your plans are")
            )
        )
    }

    var showJobDetails by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Post-Flight Checklist",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Row {
                        IconButton(onClick = { showJobDetails = true }) {
                            Icon(Icons.Default.Info, contentDescription = "Job Details")
                        }
                        IconButton(
                            onClick = onDismiss,
                            colors = IconButtonDefaults.iconButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = "Exit checklist")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Checklist items
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    checklistItems.forEach { item ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = item.isChecked,
                                onCheckedChange = { checked ->
                                    checklistItems = checklistItems.map {
                                        if (it.id == item.id) it.copy(isChecked = checked)
                                        else it
                                    }
                                }
                            )
                            Text(
                                text = item.text,
                                modifier = Modifier.weight(1f),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Complete button (enabled when all items are checked OR only 4th item is checked for development)
                Button(
                    onClick = onComplete,
                    enabled = canCompleteChecklist(checklistItems),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Complete Post-Flight Checklist")
                }
            }
        }
    }

    // Job Details Dialog
    if (showJobDetails) {
        JobDetailsDialog(
            onDismiss = { showJobDetails = false },
            viewModel = jobViewModel,
            counterViewModel = counterViewModel
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostJobChecklistDialog(
    onDismiss: () -> Unit,
    onComplete: () -> Unit,
    jobViewModel: JobViewModel,
    counterViewModel: CounterViewModel
) {
    val coroutineScope = rememberCoroutineScope()
    var checklistItems by remember {
        mutableStateOf(
            listOf(
                ChecklistItem("receipts", "All receipts saved from travel"),
                ChecklistItem("charge_equipment", "Controller, batteries, headlamp, and beacon light are charged or charging"),
                ChecklistItem("remove_sd", "Remove SD card from drone and connect to your computer"),
                ChecklistItem("upload_files", "Upload all flight files into shared folder and rename with property name, date, and drone"),
                ChecklistItem("erase_sd", "Erase SD card once all files are uploaded and install it back into drone")
            )
        )
    }

    var clientNotes by remember { mutableStateOf("") }

    var showJobDetails by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Post-Job Checklist",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Row {
                        IconButton(onClick = { showJobDetails = true }) {
                            Icon(Icons.Default.Info, contentDescription = "Job Details")
                        }
                        IconButton(
                            onClick = onDismiss,
                            colors = IconButtonDefaults.iconButtonColors(
                                contentColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = "Exit checklist")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Checklist items
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    checklistItems.forEach { item ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = item.isChecked,
                                onCheckedChange = { checked ->
                                    checklistItems = checklistItems.map {
                                        if (it.id == item.id) it.copy(isChecked = checked)
                                        else it
                                    }
                                }
                            )
                            Text(
                                text = item.text,
                                modifier = Modifier.weight(1f),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Client Notes section
                Text(
                    text = "Client Notes (Optional)",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                OutlinedTextField(
                    value = clientNotes,
                    onValueChange = { clientNotes = it },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3,
                    placeholder = { Text("Enter any notes from the client") }
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Complete button (enabled when all items are checked OR only 4th item is checked for development)
                Button(
                    onClick = {
                        // Update the survey response with client notes before completing
                        coroutineScope.launch {
                            counterViewModel.updateClientNotes(clientNotes)
                            onComplete()
                        }
                    },
                    enabled = canCompleteChecklist(checklistItems),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Check, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Complete Post-Job Checklist")
                }
            }
        }
    }

    // Job Details Dialog
    if (showJobDetails) {
        JobDetailsDialog(
            onDismiss = { showJobDetails = false },
            viewModel = jobViewModel,
            counterViewModel = counterViewModel
        )
    }
} 