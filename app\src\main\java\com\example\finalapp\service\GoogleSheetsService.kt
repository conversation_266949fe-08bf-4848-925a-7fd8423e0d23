package com.example.finalapp.service

import android.content.Context
import android.util.Log
import com.example.finalapp.R
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.model.Job
import com.example.finalapp.model.SurveyResponse
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.sheets.v4.Sheets
import com.google.api.services.sheets.v4.SheetsScopes
import com.google.api.services.sheets.v4.model.*
import com.google.auth.http.HttpCredentialsAdapter
import com.google.auth.oauth2.GoogleCredentials
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.min
import kotlin.math.pow

/**
 * Google Sheets Service for exporting survey data with rate limiting protection and performance optimization.
 *
 * ## Overview
 * This service handles the complete export of wildlife survey data to Google Sheets, including:
 * - Timeline data showing chronological counter changes
 * - Total count summaries by species and gender/age
 * - Flight summary information with survey details
 * - Central logbook entries for flight tracking
 *
 * ## Architecture
 * The service creates multiple types of sheets for each export:
 *
 * ### Timeline Sheets
 * - `{Pasture Name} Timeline`: Main timeline showing all counter changes chronologically
 * - `{Pasture Name} Timeline Backup {Pilot Name}`: Hidden backup with current pilot's data only
 *
 * ### Total Sheets
 * - `{Pasture Name} Total`: Combined totals from all pilots (accumulates data)
 * - `{Pasture Name} Total Backup {Pilot Name}`: Hidden backup with current pilot's data only
 *
 * ### Summary Sheets
 * - `{Pilot Name} Flight Summary`: Flight details, weather, and survey information
 * - `Logbook - 24/25`: Central logbook with all flight entries (separate spreadsheet)
 *
 * ## Rate Limiting & Performance
 *
 * ### Rate Limiting Strategy
 * - Google Sheets API limit: 300 requests/minute per project
 * - Conservative limit: 250 requests/minute with sliding window tracking
 * - Exponential backoff with jitter for failed requests (1s, 2s, 4s delays)
 * - Automatic retry up to 3 attempts for transient failures
 *
 * ### Performance Optimizations
 * - **Batch Operations**: Multiple requests combined into single API calls
 * - **Sheet ID Caching**: Avoids repeated metadata lookups
 * - **Request Queuing**: Prevents rate limit violations
 * - **Efficient Data Structures**: Minimizes memory usage during large exports
 *
 * ## Error Handling
 * - Automatic detection of rate limiting errors (429, quota, limit keywords)
 * - Exponential backoff with randomized jitter to prevent thundering herd
 * - Graceful degradation: continues export even if non-critical operations fail
 * - Comprehensive logging for debugging and monitoring
 *
 * ## Usage Example
 * ```kotlin
 * val sheetsService = GoogleSheetsService(context)
 * val success = sheetsService.exportJobData(
 *     spreadsheetId = "your-spreadsheet-id",
 *     job = job,
 *     pastures = pastures,
 *     history = counterHistory,
 *     surveyResponse = surveyResponse
 * )
 * if (success) {
 *     // Export completed successfully
 * } else {
 *     // Handle export failure
 * }
 * ```
 *
 * ## Authentication
 * Uses service account authentication with credentials stored in:
 * `app/src/main/res/raw/skysenderos_459013_8c4a24330608.json`
 *
 * Required Google Sheets API scopes:
 * - `https://www.googleapis.com/auth/spreadsheets`
 */
class GoogleSheetsService(private val context: Context) {
    private val TAG = "GoogleSheetsService"
    private val jsonFactory = GsonFactory.getDefaultInstance()
    private val httpTransport = GoogleNetHttpTransport.newTrustedTransport()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    // Rate limiting constants
    private val MAX_REQUESTS_PER_MINUTE = 250 // Conservative limit below Google's 300
    private val REQUEST_WINDOW_MS = 60_000L // 1 minute
    private val MAX_RETRIES = 3
    private val BASE_DELAY_MS = 1000L

    // Request tracking for rate limiting
    private val requestTimestamps = mutableListOf<Long>()
    private val requestMutex = kotlinx.coroutines.sync.Mutex()

    // Cache for sheet IDs to avoid repeated API calls
    private val sheetIdCache = ConcurrentHashMap<String, Int>()

    /**
     * Creates and returns a configured Google Sheets service instance.
     * Uses service account credentials for authentication.
     */
    private suspend fun getSheetsService(): Sheets {
        return withContext(Dispatchers.IO) {
            try {
                val credentials = GoogleCredentials.fromStream(context.resources.openRawResource(R.raw.skysenderos_459013_8c4a24330608))
                    .createScoped(listOf(SheetsScopes.SPREADSHEETS))

                Sheets.Builder(httpTransport, jsonFactory, HttpCredentialsAdapter(credentials))
                    .setApplicationName("Sky Senderos")
                    .build()
            } catch (e: Exception) {
                Log.e(TAG, "Error creating Sheets service: ${e.message}", e)
                throw e
            }
        }
    }

    /**
     * Rate limiting mechanism to prevent exceeding Google Sheets API limits.
     * Tracks requests within a sliding window and delays execution if necessary.
     */
    private suspend fun enforceRateLimit() {
        requestMutex.lock()
        try {
            val now = System.currentTimeMillis()

            // Remove timestamps older than the window
            requestTimestamps.removeAll { it < now - REQUEST_WINDOW_MS }

            // If we're at the limit, wait until we can make another request
            if (requestTimestamps.size >= MAX_REQUESTS_PER_MINUTE) {
                val oldestRequest = requestTimestamps.minOrNull() ?: now
                val waitTime = REQUEST_WINDOW_MS - (now - oldestRequest) + 100 // Add small buffer
                if (waitTime > 0) {
                    Log.d(TAG, "Rate limit reached, waiting ${waitTime}ms")
                    delay(waitTime)
                }
                // Clean up old timestamps again after waiting
                val newNow = System.currentTimeMillis()
                requestTimestamps.removeAll { it < newNow - REQUEST_WINDOW_MS }
            }

            // Record this request
            requestTimestamps.add(System.currentTimeMillis())
        } finally {
            requestMutex.unlock()
        }
    }

    /**
     * Executes an API call with retry logic and exponential backoff.
     * Handles rate limiting errors and other transient failures.
     */
    private suspend fun <T> executeWithRetry(operation: suspend () -> T): T {
        var lastException: Exception? = null

        repeat(MAX_RETRIES) { attempt ->
            try {
                enforceRateLimit()
                return operation()
            } catch (e: Exception) {
                lastException = e
                val errorMessage = e.message?.lowercase() ?: ""

                // Check if it's a rate limiting error
                val isRateLimitError = errorMessage.contains("rate") ||
                                     errorMessage.contains("quota") ||
                                     errorMessage.contains("limit") ||
                                     errorMessage.contains("429")

                if (isRateLimitError || attempt < MAX_RETRIES - 1) {
                    val delayMs = (BASE_DELAY_MS * (2.0.pow(attempt))).toLong()
                    val jitter = (Math.random() * 0.1 * delayMs).toLong() // Add 10% jitter
                    val totalDelay = delayMs + jitter

                    Log.w(TAG, "API call failed (attempt ${attempt + 1}/$MAX_RETRIES), retrying in ${totalDelay}ms: ${e.message}")
                    delay(totalDelay)
                } else {
                    Log.e(TAG, "API call failed after $MAX_RETRIES attempts", e)
                    throw e
                }
            }
        }

        throw lastException ?: Exception("Unknown error after retries")
    }

    // TODO: export the SurveyResponse into the sheet as (survey details)

    /**
     * Gets the last filled row in a sheet by checking column B.
     * Uses retry mechanism to handle transient API failures.
     *
     * @param service The Sheets service instance
     * @param spreadsheetId The ID of the spreadsheet
     * @param sheetName The name of the sheet to check
     * @return The number of the last filled row (0-based)
     */
    private suspend fun getLastFilledRow(
        service: Sheets,
        spreadsheetId: String,
        sheetName: String
    ): Int {
        return withContext(Dispatchers.IO) {
            try {
                executeWithRetry {
                    val range = "$sheetName!B:B" // Check column B for filled rows
                    val response = service.spreadsheets().values().get(spreadsheetId, range).execute()
                    val values = response.getValues()
                    if (values == null || values.isEmpty()) 0 else values.size
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting last filled row for $sheetName: ${e.message}", e)
                0
            }
        }
    }

    /**
     * Populates timeline data for a specific sheet with retry mechanism and optimized API calls.
     *
     * @param spreadsheetId The ID of the spreadsheet
     * @param pasture The pasture data to populate
     * @param history Map of pasture names to their counter change history
     * @param sheetName The name of the sheet to populate
     * @param service The Sheets service instance
     * @param droneName The name of the drone used (optional)
     * @param startRow The row to start writing at (0-based)
     */
    private suspend fun populateTimelineSheet(
        spreadsheetId: String,
        pasture: Pasture,
        history: Map<String, List<CounterChange>>,
        sheetName: String,
        service: Sheets,
        droneName: String = "",
        startRow: Int = 0
    ) {
        try {
            // Get history for this pasture from the history map
            val pastureHistory = history[pasture.name] ?: emptyList()
            val sortedHistory = pastureHistory.sortedBy { it.timestamp }
            Log.d(TAG, "Populating timeline for pasture: ${pasture.name} in sheet: $sheetName at row $startRow")
            Log.d(TAG, "History entries found: ${sortedHistory.size}")

            // Prepare data with proper structure
            val droneRow = if (droneName.isNotEmpty()) listOf(listOf("", "Drone: $droneName")) else listOf(listOf("", ""))
            val headerRow = listOf(listOf("", "SPECIES", "GENDER/AGE", "QUANTITY", "TIMESTAMP"))
            val centralTimeZone = java.util.TimeZone.getTimeZone("America/Chicago")
            val timeFormat = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).apply {
                timeZone = centralTimeZone
            }
            val timelineRows = sortedHistory.map { change ->
                val parts = change.counterName.split("-", limit = 2)
                val species = parts[0].trim()
                val genderAge = if (parts.size > 1) parts[1].trim() else "n/a"
                val quantity = change.changeAmount.toString()
                val timestamp = timeFormat.format(java.util.Date(change.timestamp))
                listOf("", species, genderAge, quantity, timestamp)
            }
            val allRows = droneRow + headerRow + timelineRows

            // Find the next empty row in column B
            val nextRow = getLastFilledRow(service, spreadsheetId, sheetName)
            val rangeStart = nextRow + 1 // 1-based for Sheets API
            val rangeEnd = nextRow + allRows.size
            val range = "$sheetName!A$rangeStart:E$rangeEnd"
            val valueRange = com.google.api.services.sheets.v4.model.ValueRange().setValues(allRows)

            // Write data with retry mechanism
            executeWithRetry {
                service.spreadsheets().values()
                    .update(spreadsheetId, range, valueRange)
                    .setValueInputOption("USER_ENTERED")
                    .execute()
            }
            // Apply formatting with retry mechanism
            val requests = mutableListOf<com.google.api.services.sheets.v4.model.Request>()
            val sheetId = getSheetIdCached(service, spreadsheetId, sheetName)

            // Auto-resize columns to fit content
            requests.add(
                com.google.api.services.sheets.v4.model.Request().setAutoResizeDimensions(
                    com.google.api.services.sheets.v4.model.AutoResizeDimensionsRequest()
                        .setDimensions(com.google.api.services.sheets.v4.model.DimensionRange()
                            .setSheetId(sheetId)
                            .setDimension("COLUMNS")
                            .setStartIndex(0)
                            .setEndIndex(5))
                )
            )

            // Bold drone row in B (first row of this block)
            requests.add(
                com.google.api.services.sheets.v4.model.Request().setRepeatCell(
                    com.google.api.services.sheets.v4.model.RepeatCellRequest()
                        .setRange(com.google.api.services.sheets.v4.model.GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(nextRow)
                            .setEndRowIndex(nextRow + 1)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(2))
                        .setCell(com.google.api.services.sheets.v4.model.CellData().setUserEnteredFormat(
                            com.google.api.services.sheets.v4.model.CellFormat().setTextFormat(
                                com.google.api.services.sheets.v4.model.TextFormat().setBold(true)
                            )
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )

            // Bold table headers in row after drone row
            requests.add(
                com.google.api.services.sheets.v4.model.Request().setRepeatCell(
                    com.google.api.services.sheets.v4.model.RepeatCellRequest()
                        .setRange(com.google.api.services.sheets.v4.model.GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(nextRow + 1)
                            .setEndRowIndex(nextRow + 2)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(5))
                        .setCell(com.google.api.services.sheets.v4.model.CellData().setUserEnteredFormat(
                            com.google.api.services.sheets.v4.model.CellFormat().setTextFormat(
                                com.google.api.services.sheets.v4.model.TextFormat().setBold(true)
                            )
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )

            // Add borders to the table (starting from header row, column B)
            val tableStartRow = nextRow + 1 // header row
            val tableEndRow = tableStartRow + 1 + timelineRows.size // +1 for header, +timelineRows.size for data
            val tableStartCol = 1 // column B
            val tableEndCol = 5 // column E

            // Single border request for the entire table
            requests.add(
                com.google.api.services.sheets.v4.model.Request().setUpdateBorders(
                    com.google.api.services.sheets.v4.model.UpdateBordersRequest()
                        .setRange(com.google.api.services.sheets.v4.model.GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(tableStartRow)
                            .setEndRowIndex(tableEndRow)
                            .setStartColumnIndex(tableStartCol)
                            .setEndColumnIndex(tableEndCol))
                        .setTop(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                        .setBottom(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                        .setLeft(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                        .setRight(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                        .setInnerHorizontal(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                        .setInnerVertical(com.google.api.services.sheets.v4.model.Border().setStyle("SOLID"))
                )
            )

            // Apply all formatting requests with retry mechanism
            val batchUpdateRequest = com.google.api.services.sheets.v4.model.BatchUpdateSpreadsheetRequest().setRequests(requests)
            executeWithRetry {
                service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
            }

            // Ensure columns are properly auto-resized after all formatting
            autoResizeColumns(service, spreadsheetId, sheetName, 5)

            Log.d(TAG, "Successfully populated timeline for pasture: ${pasture.name} in sheet: $sheetName at row $nextRow")
        } catch (e: Exception) {
            Log.e(TAG, "Error populating timeline for pasture ${pasture.name} in sheet $sheetName: ${e.message}", e)
            throw e
        }
    }

    /**
     * Exports timeline data for all pastures to Google Sheets.
     * Creates timeline sheets showing chronological counter changes for each pasture.
     *
     * This method:
     * - Creates timeline sheets for each pasture if they don't exist
     * - Creates backup timeline sheets for the current pilot
     * - Populates sheets with counter change history in chronological order
     * - Applies proper formatting and styling
     *
     * @param spreadsheetId The ID of the target Google Spreadsheet
     * @param pastures List of pastures to export timeline data for
     * @param history Map of pasture names to their counter change history
     * @param service The authenticated Google Sheets service instance
     * @param job The job containing pilot and drone information
     * @param pilotName The name of the current pilot for backup sheets
     */
    private suspend fun exportPastureTimelines(
        spreadsheetId: String,
        pastures: List<Pasture>,
        history: Map<String, List<CounterChange>>,
        service: Sheets,
        job: Job,
        pilotName: String
    ) {
        val spreadsheet = service.spreadsheets().get(spreadsheetId).execute()
        val existingSheets = spreadsheet.sheets.map { it.properties.title }
        val sheetRequests = mutableListOf<Request>()
        pastures.forEach { pasture ->
            val timelineSheetName = "${pasture.name} Timeline"
            val backupTimelineSheetName = "${pasture.name} Timeline Backup ${pilotName}"
            if (!existingSheets.contains(timelineSheetName)) {
                sheetRequests.add(
                    Request().setAddSheet(
                        AddSheetRequest().setProperties(
                            SheetProperties().setTitle(timelineSheetName)
                        )
                    )
                )
            }
            if (!existingSheets.contains(backupTimelineSheetName)) {
                sheetRequests.add(
                    Request().setAddSheet(
                        AddSheetRequest().setProperties(
                            SheetProperties().setTitle(backupTimelineSheetName).setHidden(true)
                        )
                    )
                )
            }
        }
        if (sheetRequests.isNotEmpty()) {
            val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(sheetRequests)
            service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
        }
        // Populate main timeline sheets (append below last filled row, with 1 empty row)
        pastures.forEach { pasture ->
            val timelineSheetName = "${pasture.name} Timeline"
            val droneName = job.pilotInfo?.droneName ?: "DJI Matrice 4T"
            Log.d("GoogleSheetsService", "Populating main timeline sheet: $timelineSheetName")
            val lastRow = getLastFilledRow(service, spreadsheetId, timelineSheetName)
            val startRow = if (lastRow == 0) 0 else lastRow + 1 // 1 empty row between blocks
            populateTimelineSheet(spreadsheetId, pasture, history, timelineSheetName, service, droneName, startRow)
        }
        // Populate backup timeline sheets (overwrite from top)
        pastures.forEach { pasture ->
            val backupTimelineSheetName = "${pasture.name} Timeline Backup ${pilotName}"
            val droneName = job.pilotInfo?.droneName ?: "DJI Matrice 4T"
            Log.d("GoogleSheetsService", "Populating backup timeline sheet: $backupTimelineSheetName")
            populateTimelineSheet(spreadsheetId, pasture, history, backupTimelineSheetName, service, droneName, 0)
        }
    }

    /**
     * Populates a combined total sheet that accumulates data from multiple pilots.
     * This method reads existing data from the sheet and adds new counts to it,
     * allowing multiple pilots to contribute to the same totals.
     *
     * The sheet structure includes:
     * - Row 1: Pasture name
     * - Row 2: "COMBINED TOTALS FROM ALL PILOTS" label
     * - Row 3: Headers (SPECIES, GENDER/AGE, INITIAL COUNT, REVIEWED COUNT, TOTAL)
     * - Subsequent rows: Species/gender data with accumulated counts
     *
     * @param spreadsheetId The ID of the target Google Spreadsheet
     * @param pasture The pasture data to add to the combined totals
     * @param sheetName The name of the sheet to populate
     * @param service The authenticated Google Sheets service instance
     * @param allPastures List of all pastures (for context, currently unused)
     */
    private suspend fun populateCombinedTotalSheet(
        spreadsheetId: String,
        pasture: Pasture,
        sheetName: String,
        service: Sheets,
        allPastures: List<Pasture>
    ) {
        try {
            Log.d(TAG, "[TotalSheet] Populating combined total for pasture: ${pasture.name} in sheet: $sheetName")
            
            // Group current export counters by species and gender/age
            val newGroupedCounters = pasture.counters.groupBy { counter ->
                val parts = counter.name.split("-", limit = 2)
                val species = parts[0].trim()
                val genderAge = if (parts.size > 1) parts[1].trim() else "n/a"
                Pair(species, genderAge)
            }

            // Read existing data from the main sheet (skip header rows) with retry mechanism
            val readRange = "$sheetName!B4:D" // B4 = first data row after headers, D = INITIAL COUNT column
            val values = executeWithRetry {
                val response = service.spreadsheets().values().get(spreadsheetId, readRange).execute()
                response.getValues() ?: emptyList()
            }
            
            // Map of (species, genderAge) -> current initial count
            val existingTotals = mutableMapOf<Pair<String, String>, Int>()
            for (row in values) {
                if (row.size >= 3) {
                    val species = row[0].toString().trim()
                    val genderAge = row[1].toString().trim()
                    val initialCount = row[2].toString().toIntOrNull() ?: 0
                    if (species.isNotEmpty() && species != "SPECIES") { // Skip header row
                        existingTotals[Pair(species, genderAge)] = initialCount
                        Log.d(TAG, "[populateCombinedTotalSheet] Found existing initial count: species='$species', genderAge='$genderAge', initialCount=$initialCount")
                    }
                }
            }

            // Add new data to existing totals (ACCUMULATE)
            for ((speciesGender, counters) in newGroupedCounters) {
                val newCount = counters.sumOf { it.value }
                existingTotals[speciesGender] = (existingTotals[speciesGender] ?: 0) + newCount
                Log.d(TAG, "[populateCombinedTotalSheet] Adding $newCount to existing count for ${speciesGender.first}/${speciesGender.second}")
            }
            
            // Prepare data with proper structure
            val pastureNameRow = listOf(listOf("", pasture.name)) // Row 1: Pasture name in column B
            val combinedRow = listOf(listOf("", "COMBINED TOTALS FROM ALL PILOTS")) // Row 2: Combined label
            val headerRow = listOf(listOf("", "SPECIES", "GENDER/AGE", "INITIAL COUNT", "REVIEWED COUNT", "TOTAL")) // Row 3: Headers
            
            // Sort by species, then gender/age
            val sortedKeys = existingTotals.keys.sortedWith(compareBy({ it.first }, { it.second }))
            val totalRows = sortedKeys.map { (species, genderAge) ->
                val initialCount = existingTotals[Pair(species, genderAge)] ?: 0
                listOf("", species, genderAge, initialCount.toString(), "", "") // Initial count in column D (index 3)
            }
            
            val allRows = pastureNameRow + combinedRow + headerRow + totalRows
            val writeRange = "$sheetName!A1:F${allRows.size}"
            val valueRange = ValueRange().setValues(allRows)
            
            // Write data with retry mechanism
            executeWithRetry {
                service.spreadsheets().values()
                    .update(spreadsheetId, writeRange, valueRange)
                    .setValueInputOption("USER_ENTERED")
                    .execute()
            }

            // Apply formatting
            val requests = mutableListOf<Request>()
            val sheetId = getSheetIdCached(service, spreadsheetId, sheetName)

            // Clear all borders first
            requests.add(
                Request().setUpdateBorders(
                    UpdateBordersRequest()
                        .setRange(GridRange().setSheetId(sheetId))
                        .setTop(Border().setStyle("NONE"))
                        .setBottom(Border().setStyle("NONE"))
                        .setLeft(Border().setStyle("NONE"))
                        .setRight(Border().setStyle("NONE"))
                        .setInnerHorizontal(Border().setStyle("NONE"))
                        .setInnerVertical(Border().setStyle("NONE"))
                )
            )

            // Bold pasture name in B1
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(0)
                            .setEndRowIndex(1)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(2))
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )

            // Bold combined totals row in B2
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(1)
                            .setEndRowIndex(2)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(2))
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true).setItalic(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold,userEnteredFormat.textFormat.italic")
                )
            )

            // Bold table headers in row 3 (index 2)
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(2)
                            .setEndRowIndex(3)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(6)) // B to F columns (full header row)
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )

            // Add borders around the table ONLY (headers + data, columns B to F)
            val tableStartRow = 2 // Row 3 (header row) - 0-indexed
            val tableEndRow = tableStartRow + 1 + totalRows.size // +1 for header, +totalRows.size for data
            val tableStartCol = 1 // Column B - 0-indexed
            val tableEndCol = 6 // Column F - 0-indexed (end exclusive)

            requests.add(
                Request().setUpdateBorders(
                    UpdateBordersRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(tableStartRow)
                            .setEndRowIndex(tableEndRow)
                            .setStartColumnIndex(tableStartCol)
                            .setEndColumnIndex(tableEndCol))
                        .setTop(Border().setStyle("SOLID"))
                        .setBottom(Border().setStyle("SOLID"))
                        .setLeft(Border().setStyle("SOLID"))
                        .setRight(Border().setStyle("SOLID"))
                        .setInnerHorizontal(Border().setStyle("SOLID"))
                        .setInnerVertical(Border().setStyle("SOLID"))
                )
            )

            // Apply all formatting requests with retry mechanism
            val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(requests)
            executeWithRetry {
                service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
            }

            // Ensure columns are properly auto-resized after all formatting
            autoResizeColumns(service, spreadsheetId, sheetName, 6)

            Log.d(TAG, "Successfully populated combined total for pasture: ${pasture.name} in sheet: $sheetName")
        } catch (e: Exception) {
            Log.e("GoogleSheetsService", "Error populating combined total for pasture ${pasture.name} in sheet $sheetName: ${e.message}", e)
            throw e
        }
    }

    /**
     * Populates a total sheet with data from a single pilot (no accumulation).
     * This method creates backup sheets that contain only the current pilot's data,
     * without reading or combining with existing data.
     *
     * The sheet structure includes:
     * - Row 1: Pasture name
     * - Row 2: Headers (SPECIES, GENDER/AGE, INITIAL COUNT, REVIEWED COUNT, TOTAL)
     * - Data rows: Species/gender data with current pilot's counts only
     * - Final row: Total sum of all counts
     *
     * @param spreadsheetId The ID of the target Google Spreadsheet
     * @param pasture The pasture data to populate
     * @param sheetName The name of the sheet to populate
     * @param service The authenticated Google Sheets service instance
     */
    private suspend fun populateTotalSheet(
        spreadsheetId: String,
        pasture: Pasture,
        sheetName: String,
        service: Sheets
    ) {
        try {
            Log.d(TAG, "[populateTotalSheet] Populating backup total for pasture: ${pasture.name} in sheet: $sheetName")
            
            // Group counters by species and gender/age (current pilot's data only)
            val groupedCounters = pasture.counters.groupBy { counter ->
                val parts = counter.name.split("-", limit = 2)
                val species = parts[0].trim()
                val genderAge = if (parts.size > 1) parts[1].trim() else "n/a"
                Pair(species, genderAge)
            }

            // Prepare data with proper structure (NO reading of existing data)
            val pastureNameRow = listOf(listOf("", pasture.name)) // Row 1: Pasture name in column B
            val headerRow = listOf(listOf("", "SPECIES", "GENDER/AGE", "INITIAL COUNT", "REVIEWED COUNT", "TOTAL")) // Row 2: Headers
            
            // Create rows for current pilot's data only
            val totalRows = groupedCounters.map { (speciesGender, counters) ->
                val species = speciesGender.first
                val genderAge = speciesGender.second
                val initialCount = counters.sumOf { it.value }
                listOf("", species, genderAge, initialCount.toString(), "", "") // Initial count in column D (index 3)
            }.sortedWith(compareBy({ it[1] }, { it[2] })) // Sort by species, then gender/age
            
            // Add a sum row for the INITIAL COUNT column
            val totalSum = totalRows.sumOf { it[3].toIntOrNull() ?: 0 }
            val sumRow = listOf("", "", "TOTAL SUM", totalSum.toString(), "", "")

            val allRows = pastureNameRow + headerRow + totalRows + listOf(sumRow)
            val writeRange = "$sheetName!A1:F${allRows.size}"
            val valueRange = ValueRange().setValues(allRows)
            
            // Write data with retry mechanism
            executeWithRetry {
                service.spreadsheets().values()
                    .update(spreadsheetId, writeRange, valueRange)
                    .setValueInputOption("USER_ENTERED")
                    .execute()
            }

            // Apply formatting
            val requests = mutableListOf<Request>()
            val sheetId = getSheetIdCached(service, spreadsheetId, sheetName)
            
            // Clear all borders first
            requests.add(
                Request().setUpdateBorders(
                    UpdateBordersRequest()
                        .setRange(GridRange().setSheetId(sheetId))
                        .setTop(Border().setStyle("NONE"))
                        .setBottom(Border().setStyle("NONE"))
                        .setLeft(Border().setStyle("NONE"))
                        .setRight(Border().setStyle("NONE"))
                        .setInnerHorizontal(Border().setStyle("NONE"))
                        .setInnerVertical(Border().setStyle("NONE"))
                )
            )
            
            // Bold pasture name in B1
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(0)
                            .setEndRowIndex(1)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(2))
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )
            
            // Bold table headers in row 2 (index 1)
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(1)
                            .setEndRowIndex(2)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(6)) // B to F columns (full header row)
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )
            
            // Bold the sum row
            val sumRowIndex = allRows.size - 1
            requests.add(
                Request().setRepeatCell(
                    RepeatCellRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(sumRowIndex)
                            .setEndRowIndex(sumRowIndex + 1)
                            .setStartColumnIndex(1)
                            .setEndColumnIndex(6)) // B to F columns (full sum row)
                        .setCell(CellData().setUserEnteredFormat(
                            CellFormat().setTextFormat(TextFormat().setBold(true))
                        ))
                        .setFields("userEnteredFormat.textFormat.bold")
                )
            )
            
            // Add borders around the table ONLY (headers + data + sum row, columns B to F)
            val tableStartRow = 1 // Row 2 (header row) - 0-indexed
            val tableEndRow = tableStartRow + 1 + totalRows.size + 1 // +1 for header, +totalRows.size for data, +1 for sum row
            val tableStartCol = 1 // Column B - 0-indexed
            val tableEndCol = 6 // Column F - 0-indexed (end exclusive)

            requests.add(
                Request().setUpdateBorders(
                    UpdateBordersRequest()
                        .setRange(GridRange()
                            .setSheetId(sheetId)
                            .setStartRowIndex(tableStartRow)
                            .setEndRowIndex(tableEndRow)
                            .setStartColumnIndex(tableStartCol)
                            .setEndColumnIndex(tableEndCol))
                        .setTop(Border().setStyle("SOLID"))
                        .setBottom(Border().setStyle("SOLID"))
                        .setLeft(Border().setStyle("SOLID"))
                        .setRight(Border().setStyle("SOLID"))
                        .setInnerHorizontal(Border().setStyle("SOLID"))
                        .setInnerVertical(Border().setStyle("SOLID"))
                )
            )
            
            requests.add(
                Request().setAutoResizeDimensions(
                    AutoResizeDimensionsRequest()
                        .setDimensions(DimensionRange()
                            .setSheetId(sheetId)
                            .setDimension("COLUMNS")
                            .setStartIndex(0)
                            .setEndIndex(7))
                )
            )

            // Apply all formatting requests with retry mechanism
            val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(requests)
            executeWithRetry {
                service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
            }

            Log.d(TAG, "Successfully populated backup total for pasture: ${pasture.name} in sheet: $sheetName")
        } catch (e: Exception) {
            Log.e("GoogleSheetsService", "Error populating backup total for pasture ${pasture.name} in sheet $sheetName: ${e.message}", e)
            throw e
        }
    }

    /**
     * Exports total count data for all pastures to Google Sheets.
     * Creates both main total sheets (combined data) and backup total sheets (pilot-specific data).
     *
     * This method:
     * - Creates main total sheets for each pasture (visible, accumulates data from all pilots)
     * - Creates backup total sheets for each pasture (hidden, contains only current pilot's data)
     * - Populates both types of sheets with appropriate data
     *
     * @param spreadsheetId The ID of the target Google Spreadsheet
     * @param pastures List of pastures to export total data for
     * @param service The authenticated Google Sheets service instance
     * @param pilotName The name of the current pilot for backup sheet naming
     */
    private suspend fun exportPastureTotals(
        spreadsheetId: String,
        pastures: List<Pasture>,
        service: Sheets,
        pilotName: String
    ) {
        val existingSheets = executeWithRetry {
            val spreadsheet = service.spreadsheets().get(spreadsheetId).execute()
            spreadsheet.sheets.map { it.properties.title }
        }
        val sheetRequests = mutableListOf<Request>()

        pastures.forEach { pasture ->
            val totalSheetName = "${pasture.name} Total"
            val backupTotalSheetName = "${pasture.name} Total Backup ${pilotName}"
            
            if (!existingSheets.contains(totalSheetName)) {
                sheetRequests.add(
                    Request().setAddSheet(
                        AddSheetRequest().setProperties(
                            SheetProperties().setTitle(totalSheetName)
                        )
                    )
                )
            }
            
            if (!existingSheets.contains(backupTotalSheetName)) {
                sheetRequests.add(
                    Request().setAddSheet(
                        AddSheetRequest().setProperties(
                            SheetProperties().setTitle(backupTotalSheetName).setHidden(true)
                        )
                    )
                )
            }
        }
        if (sheetRequests.isNotEmpty()) {
            val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(sheetRequests)
            executeWithRetry {
                service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
            }
        }

        // Populate main total sheets
        pastures.forEach { pasture ->
            val totalSheetName = "${pasture.name} Total"
            populateCombinedTotalSheet(spreadsheetId, pasture, totalSheetName, service, pastures)
        }

        // Populate backup total sheets
        pastures.forEach { pasture ->
            val backupTotalSheetName = "${pasture.name} Total Backup ${pilotName}"
            populateTotalSheet(spreadsheetId, pasture, backupTotalSheetName, service)
        }
    }

    /**
     * Gets the sheet ID for a given sheet name by querying the spreadsheet metadata.
     * This method should be called through getSheetIdCached to benefit from caching.
     */
    private fun getSheetId(service: Sheets, spreadsheetId: String, sheetName: String): Int {
        val spreadsheet = service.spreadsheets().get(spreadsheetId).execute()
        return spreadsheet.sheets.find { it.properties.title == sheetName }?.properties?.sheetId ?: 0
    }

    /**
     * Gets the sheet ID for a given sheet name, with caching to avoid repeated API calls.
     * This is a performance optimization since sheet IDs are needed for formatting operations.
     */
    private suspend fun getSheetIdCached(service: Sheets, spreadsheetId: String, sheetName: String): Int {
        val cacheKey = "$spreadsheetId:$sheetName"
        return sheetIdCache.getOrPut(cacheKey) {
            executeWithRetry {
                getSheetId(service, spreadsheetId, sheetName)
            }
        }
    }

    /**
     * Auto-resizes all columns in a sheet to fit their content.
     * This is a dedicated function to ensure proper column sizing.
     */
    private suspend fun autoResizeColumns(
        service: Sheets,
        spreadsheetId: String,
        sheetName: String,
        maxColumns: Int = 8
    ) {
        try {
            val sheetId = getSheetIdCached(service, spreadsheetId, sheetName)
            val request = Request().setAutoResizeDimensions(
                AutoResizeDimensionsRequest()
                    .setDimensions(DimensionRange()
                        .setSheetId(sheetId)
                        .setDimension("COLUMNS")
                        .setStartIndex(0)
                        .setEndIndex(maxColumns))
            )

            val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(listOf(request))
            executeWithRetry {
                service.spreadsheets().batchUpdate(spreadsheetId, batchUpdateRequest).execute()
            }
            Log.d(TAG, "Auto-resized columns for sheet: $sheetName")
        } catch (e: Exception) {
            Log.e(TAG, "Error auto-resizing columns for sheet $sheetName: ${e.message}", e)
        }
    }

    /**
     * Appends a logbook entry to the central 'Logbook - 24/25' sheet.
     * This method adds a single row to the logbook with flight details and survey information.
     *
     * The logbook entry includes:
     * - Property name (column A)
     * - Date and time of completion
     * - Operator name and drone information
     * - Mission types and flight details
     * - Location coordinates and remarks
     * - Pilot survey additional notes (column K)
     * - Day/night indicator (column L)
     * - Flight duration, takeoffs, and battery changes
     *
     * @param service The authenticated Google Sheets service instance
     * @param job The job containing pilot and location information
     * @param surveyResponse The survey response containing flight details and timing
     */
    private suspend fun appendLogbookEntry(
        service: Sheets,
        job: Job,
        surveyResponse: SurveyResponse,
        clientNotes: String
    ) {
        Log.d(TAG, "Appending logbook entry with client notes: '$clientNotes'")
        val spreadsheetId = "1sDIL0Khwrap1p_FAn7w-ZNQGjTjFW03UMAR_3gtfx3A"
        val sheetName = "Logbook - 24/25"
        // Find next unused row
        val nextRow = getLastFilledRow(service, spreadsheetId, sheetName) + 1 // 1-based

        // Prepare values
        val centralTimeZone = java.util.TimeZone.getTimeZone("America/Chicago")
        val dateFormat = java.text.SimpleDateFormat("MM/dd/yyyy", java.util.Locale.US).apply { timeZone = centralTimeZone }
        val timeFormat = java.text.SimpleDateFormat("hh:mm:ss a", java.util.Locale.US).apply { timeZone = centralTimeZone }
        val completionDate = java.util.Date(surveyResponse.completionTimestamp)
        val date = dateFormat.format(completionDate)
        val time = timeFormat.format(completionDate)
        val operatorName = job.pilotInfo?.name ?: ""
        val drone = job.pilotInfo?.droneName ?: ""
        // Map missionTypes to required output for column G
        val missionTypeOrder = listOf(
            "Survey" to "Game Survey",
            "Visible Camera Add-On" to "Spotlight",
            "Game Map Add-On" to "Map"
        )
        val missionTypes = missionTypeOrder
            .filter { (formValue, _) -> surveyResponse.missionTypes.contains(formValue) }
            .map { it.second }
            .joinToString(" + ")
        val coordinates = job.coordinates
        val remarks = surveyResponse.remarksProceduresManeuvers
        val dayOrNight = if (surveyResponse.isNight) "Night" else "Day"
        val takeoffs = surveyResponse.takeOffsAndLandings
        val batteryChanges = surveyResponse.batteryChanges
        
        // Add detailed logging for logbook duration calculation
        Log.d(TAG, "[Logbook] Duration calculation:")
        Log.d(TAG, "[Logbook] surveyResponse.completionTimestamp: ${surveyResponse.completionTimestamp}")
        Log.d(TAG, "[Logbook] surveyResponse.startTimestamp: ${surveyResponse.startTimestamp}")
        Log.d(TAG, "[Logbook] completionTimestamp - startTimestamp: ${surveyResponse.completionTimestamp - surveyResponse.startTimestamp}")
        
        val durationMinutes = kotlin.math.ceil((surveyResponse.completionTimestamp - surveyResponse.startTimestamp) / 60000.0).toInt()
        Log.d(TAG, "[Logbook] final durationMinutes: $durationMinutes")

        val logbookRow = listOf(
            job.name,               // A (property name)
            date,                   // B (shifted from A)
            time,                   // C (shifted from B)
            operatorName,           // D (shifted from C)
            drone,                  // E (shifted from D)
            "",                     // F (shifted from E, unchanged)
            "Commercial",           // G (shifted from F, capitalized)
            missionTypes,           // H (shifted from G, custom logic)
            coordinates,            // I (shifted from H)
            remarks,                // J (shifted from I)
            surveyResponse.additionalNotes, // K (pilot survey additional notes)
            dayOrNight,             // L (shifted from J, day or night)
            takeoffs.toString(),    // M (shifted from K)
            batteryChanges.toString(), // N (shifted from L)
            durationMinutes.toString(), // O (shifted from M)
            clientNotes             // P (shifted from N, client notes)
        )

        val range = "$sheetName!A$nextRow:P$nextRow"
        val valueRange = com.google.api.services.sheets.v4.model.ValueRange().setValues(listOf(logbookRow))
        executeWithRetry {
            service.spreadsheets().values()
                .update(spreadsheetId, range, valueRange)
                .setValueInputOption("USER_ENTERED")
                .execute()
        }
    }

    /**
     * Main method to export complete job data to Google Sheets.
     * This is the primary entry point for exporting survey data and handles the entire export process.
     *
     * Export Process:
     * 1. Validates and trims the spreadsheet ID
     * 2. Creates necessary sheets (timeline, total, flight summary)
     * 3. Exports timeline data showing chronological counter changes
     * 4. Exports total count data (both combined and pilot-specific)
     * 5. Exports flight summary information if survey response is provided
     * 6. Appends entry to the central logbook
     * 7. Applies formatting and styling to all sheets
     * 8. Removes default Sheet1 if present
     *
     * Rate Limiting & Error Handling:
     * - All API calls use exponential backoff retry mechanism
     * - Request rate is limited to prevent exceeding Google's API limits
     * - Transient errors are automatically retried up to 3 times
     * - Sheet ID caching reduces redundant API calls
     *
     * Performance Optimizations:
     * - Batch operations are used wherever possible
     * - Sheet creation is batched into single API calls
     * - Formatting requests are batched to minimize API usage
     * - Caching prevents repeated sheet metadata lookups
     *
     * @param spreadsheetId The ID of the target Google Spreadsheet (will be trimmed)
     * @param job The job containing pilot info, coordinates, and other metadata
     * @param pastures List of pastures with their counter data
     * @param history Map of pasture names to their counter change history
     * @param surveyResponse Optional survey response for flight summary and logbook
     * @return Boolean indicating whether the export was successful
     *
     * @throws Exception if export fails after all retry attempts
     */
    suspend fun exportJobData(
        spreadsheetId: String,
        job: Job,
        pastures: List<Pasture>,
        history: Map<String, List<CounterChange>>,
        surveyResponse: SurveyResponse?,
        clientNotes: String = ""
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // Trim the spreadsheet ID to remove any leading/trailing whitespace
            val trimmedSpreadsheetId = spreadsheetId.trim()
            Log.d(TAG, "Starting export for job: ${job.name} (ID: ${job.id})")
            Log.d(TAG, "Original spreadsheet ID: '$spreadsheetId'")
            Log.d(TAG, "Trimmed spreadsheet ID: '$trimmedSpreadsheetId'")
            Log.d(TAG, "Number of pastures: ${pastures.size}")
            Log.d(TAG, "History entries: ${history.size}")
            history.forEach { (pastureName, changes) ->
                Log.d(TAG, "Pasture '$pastureName' has ${changes.size} history entries")
            }
            Log.d(TAG, "Survey response provided: ${surveyResponse != null}")
            Log.d(TAG, "Client notes provided: '$clientNotes'")
            Log.d(TAG, "Client notes length: ${clientNotes.length}")
            
            // Clear the sheet ID cache to avoid issues with different spreadsheet IDs
            sheetIdCache.clear()
            
            val service = getSheetsService()

            // Get existing sheets with retry mechanism
            val existingSheets = executeWithRetry {
                val spreadsheet = service.spreadsheets().get(trimmedSpreadsheetId).execute()
                spreadsheet.sheets.map { it.properties.title }
            }

            // Create all sheets first
            val sheetRequests = mutableListOf<Request>()

            // Add flight summary sheet if needed (named with pilot name)
            val pilotName = job.pilotInfo?.name ?: "Unknown Pilot"
            val flightSummarySheetName = "${pilotName} Flight Summary"
            if (surveyResponse != null && !existingSheets.contains(flightSummarySheetName)) {
                sheetRequests.add(
                    Request().setAddSheet(
                        AddSheetRequest().setProperties(
                            SheetProperties().setTitle(flightSummarySheetName)
                        )
                    )
                )
            }

            // Execute batch update if needed with retry mechanism
            if (sheetRequests.isNotEmpty()) {
                val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(sheetRequests)
                executeWithRetry {
                    service.spreadsheets().batchUpdate(trimmedSpreadsheetId, batchUpdateRequest).execute()
                }
            }

            // Hide gridlines for all new sheets
            val hideGridlinesRequests = mutableListOf<Request>()
            
            // Hide gridlines for Flight Summary sheet if it was created
            if (surveyResponse != null && !existingSheets.contains(flightSummarySheetName)) {
                val flightSummarySheetId = getSheetId(service, trimmedSpreadsheetId, flightSummarySheetName)
                hideGridlinesRequests.add(
                    Request().setUpdateSheetProperties(
                        UpdateSheetPropertiesRequest()
                            .setProperties(SheetProperties()
                                .setSheetId(flightSummarySheetId)
                                .setGridProperties(GridProperties().setHideGridlines(true)))
                            .setFields("gridProperties.hideGridlines")
                    )
                )
            }
            
            // Execute gridline hiding requests with retry mechanism
            if (hideGridlinesRequests.isNotEmpty()) {
                val gridlinesBatchRequest = BatchUpdateSpreadsheetRequest().setRequests(hideGridlinesRequests)
                executeWithRetry {
                    service.spreadsheets().batchUpdate(trimmedSpreadsheetId, gridlinesBatchRequest).execute()
                }
            }

            // Add timeline sheets for each pasture
            exportPastureTimelines(trimmedSpreadsheetId, pastures, history, service, job, pilotName)
            
            // Add total sheets for each pasture
            exportPastureTotals(trimmedSpreadsheetId, pastures, service, pilotName)

            // Hide gridlines for all pasture sheets (main, backup, and combined)
            pastures.forEach { pasture ->
                val timelineSheetName = "${pasture.name} Timeline"
                val totalSheetName = "${pasture.name} Total"
                val backupTimelineSheetName = "${pasture.name} Timeline Backup ${pilotName}"
                val backupTotalSheetName = "${pasture.name} Total Backup ${pilotName}"
                
                // Hide gridlines for timeline sheet
                val timelineSheetId = getSheetId(service, trimmedSpreadsheetId, timelineSheetName)
                val timelineGridlinesRequest = Request().setUpdateSheetProperties(
                    UpdateSheetPropertiesRequest()
                        .setProperties(SheetProperties()
                            .setSheetId(timelineSheetId)
                            .setGridProperties(GridProperties().setHideGridlines(true)))
                        .setFields("gridProperties.hideGridlines")
                )
                
                // Hide gridlines for total sheet
                val totalSheetId = getSheetId(service, trimmedSpreadsheetId, totalSheetName)
                val totalGridlinesRequest = Request().setUpdateSheetProperties(
                    UpdateSheetPropertiesRequest()
                        .setProperties(SheetProperties()
                            .setSheetId(totalSheetId)
                            .setGridProperties(GridProperties().setHideGridlines(true)))
                        .setFields("gridProperties.hideGridlines")
                )

                // Hide gridlines for backup timeline sheet
                val backupTimelineSheetId = getSheetId(service, trimmedSpreadsheetId, backupTimelineSheetName)
                val backupTimelineGridlinesRequest = Request().setUpdateSheetProperties(
                    UpdateSheetPropertiesRequest()
                        .setProperties(SheetProperties()
                            .setSheetId(backupTimelineSheetId)
                            .setGridProperties(GridProperties().setHideGridlines(true)))
                        .setFields("gridProperties.hideGridlines")
                )
                
                // Hide gridlines for backup total sheet
                val backupTotalSheetId = getSheetId(service, trimmedSpreadsheetId, backupTotalSheetName)
                val backupTotalGridlinesRequest = Request().setUpdateSheetProperties(
                    UpdateSheetPropertiesRequest()
                        .setProperties(SheetProperties()
                            .setSheetId(backupTotalSheetId)
                            .setGridProperties(GridProperties().setHideGridlines(true)))
                        .setFields("gridProperties.hideGridlines")
                )
                
                val pastureGridlinesBatchRequest = BatchUpdateSpreadsheetRequest()
                    .setRequests(listOf(timelineGridlinesRequest, totalGridlinesRequest, backupTimelineGridlinesRequest, backupTotalGridlinesRequest))
                executeWithRetry {
                    service.spreadsheets().batchUpdate(trimmedSpreadsheetId, pastureGridlinesBatchRequest).execute()
                }
            }

            // Remove Sheet1 if it exists with retry mechanism
            val sheet1 = existingSheets.find { it == "Sheet1" }
            if (sheet1 != null) {
                executeWithRetry {
                    val currentSpreadsheet = service.spreadsheets().get(trimmedSpreadsheetId).execute()
                    val sheet1Object = currentSpreadsheet.sheets.find { it.properties.title == "Sheet1" }
                    if (sheet1Object != null) {
                        val deleteRequest = Request().setDeleteSheet(
                            DeleteSheetRequest().setSheetId(sheet1Object.properties.sheetId)
                        )
                        val deleteBatchRequest = BatchUpdateSpreadsheetRequest().setRequests(listOf(deleteRequest))
                        service.spreadsheets().batchUpdate(trimmedSpreadsheetId, deleteBatchRequest).execute()
                    }
                }
            }

            // Process flight summary if available
            if (surveyResponse != null) {
                try {
                    // Calculate flight duration using start and completion timestamps (rounded up to nearest minute)
                    val flightDurationMillis = surveyResponse.completionTimestamp - surveyResponse.startTimestamp
                    val flightDurationMinutes = (flightDurationMillis / (1000 * 60)).toDouble()
                    val flightDuration = kotlin.math.ceil(flightDurationMinutes).toInt()
                    
                    // Add detailed logging for debugging
                    Log.d(TAG, "[Flight Summary] Duration calculation:")
                    Log.d(TAG, "[Flight Summary] surveyResponse.completionTimestamp: ${surveyResponse.completionTimestamp}")
                    Log.d(TAG, "[Flight Summary] surveyResponse.startTimestamp: ${surveyResponse.startTimestamp}")
                    Log.d(TAG, "[Flight Summary] job.creationTimestamp: ${job.creationTimestamp}")
                    Log.d(TAG, "[Flight Summary] flightDurationMillis: $flightDurationMillis")
                    Log.d(TAG, "[Flight Summary] flightDurationMinutes: $flightDurationMinutes")
                    Log.d(TAG, "[Flight Summary] final flightDuration: $flightDuration")

                    // Format date and time in Central Time using completion timestamp
                    val centralTimeZone = TimeZone.getTimeZone("America/Chicago")
                    val centralDateFormat = SimpleDateFormat("MM/dd/yyyy", Locale.US).apply {
                        timeZone = centralTimeZone
                    }
                    val centralTimeFormat = SimpleDateFormat("hh:mm:ss a", Locale.US).apply {
                        timeZone = centralTimeZone
                    }
                    val completionDate = Date(surveyResponse.completionTimestamp)
                    val date = centralDateFormat.format(completionDate)
                    val time = centralTimeFormat.format(completionDate)

                    // Prepare the 18 fields in order
                    val labels = listOf(
                        "DATE", "TIME STARTED", "DURATION", "DAY TEMP.", "FLIGHT TEMP", "DRONE HEIGHT   .",
                        "PILOT 1", "PILOT 2", "PILOT 3", "REVIEWER", "DRONE", "DRONE SPEED    .",
                        "CLIENT", "TOPOGRAPHY", "COVER", "ANIMAL DENSITY", "DAY WEATHER", "TRANSECT WIDTH  ."
                    )
                    val values = listOf(
                        date,
                        time,
                        "$flightDuration min",
                        "",
                        "",
                        "125 ft",
                        job.pilotInfo?.name ?: "",
                        "",
                        "",
                        "",
                        job.pilotInfo?.droneName ?: "DJI Matrice 4T",
                        "25 mph",
                        job.name, // property_name
                        surveyResponse.topography,
                        surveyResponse.cover,
                        surveyResponse.animalDensity,
                        surveyResponse.dayWeather,
                        "360 ft"
                    )

                    // Build rows: each row has B(label),C(data),D(label),E(data),F(label),G(data)
                    val summaryRows = (0 until 6).map { i ->
                        listOf(
                            "", // A (empty)
                            labels[i], values[i],
                            labels[i+6], values[i+6],
                            labels[i+12], values[i+12]
                        )
                    }

                    val summaryRange = "$flightSummarySheetName!A2:G7"
                    val summaryValues = ValueRange().setValues(summaryRows)
                    service.spreadsheets().values()
                        .update(trimmedSpreadsheetId, summaryRange, summaryValues)
                        .setValueInputOption("USER_ENTERED")
                        .execute()

                    // Apply formatting: bold all label columns (B, D, F), add border to the table (B2:G7)
                    val sheetId = getSheetId(service, trimmedSpreadsheetId, flightSummarySheetName)
                    val requests = mutableListOf<Request>()

                    // Auto-resize columns to fit content
                    requests.add(
                        Request().setAutoResizeDimensions(
                            AutoResizeDimensionsRequest()
                                .setDimensions(DimensionRange()
                                    .setSheetId(sheetId)
                                    .setDimension("COLUMNS")
                                    .setStartIndex(0)
                                    .setEndIndex(7))
                        )
                    )

                    // Bold all labels in columns B, D, F
                    listOf(1, 3, 5).forEach { col ->
                        requests.add(
                            Request().setRepeatCell(
                                RepeatCellRequest()
                                    .setRange(
                                        GridRange()
                                            .setSheetId(sheetId)
                                            .setStartRowIndex(1)
                                            .setEndRowIndex(7)
                                            .setStartColumnIndex(col)
                                            .setEndColumnIndex(col+1)
                                    )
                                    .setCell(CellData().setUserEnteredFormat(
                                        CellFormat().setTextFormat(TextFormat().setBold(true))
                                    ))
                                    .setFields("userEnteredFormat.textFormat.bold")
                            )
                        )
                    }

                    // Add border to the entire table (B2:G7)
                    requests.add(
                        Request().setUpdateBorders(
                            UpdateBordersRequest()
                                .setRange(
                                    GridRange()
                                        .setSheetId(sheetId)
                                        .setStartRowIndex(1)
                                        .setEndRowIndex(7)
                                        .setStartColumnIndex(1)
                                        .setEndColumnIndex(7)
                                )
                                .setTop(Border().setStyle("SOLID"))
                                .setBottom(Border().setStyle("SOLID"))
                                .setLeft(Border().setStyle("SOLID"))
                                .setRight(Border().setStyle("SOLID"))
                                .setInnerHorizontal(Border().setStyle("SOLID"))
                                .setInnerVertical(Border().setStyle("SOLID"))
                        )
                    )

                    val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(requests)
                    executeWithRetry {
                        service.spreadsheets().batchUpdate(trimmedSpreadsheetId, batchUpdateRequest).execute()
                    }

                    // Ensure columns are properly auto-resized after all formatting
                    autoResizeColumns(service, trimmedSpreadsheetId, flightSummarySheetName, 7)

                } catch (e: Exception) {
                    Log.e(TAG, "Error processing survey response: "+e.message, e)
                    return@withContext false
                }
                // Append logbook entry
                try {
                    appendLogbookEntry(service, job, surveyResponse, clientNotes)
                } catch (e: Exception) {
                    Log.e(TAG, "Error appending logbook entry: "+e.message, e)
                    // Do not fail the export if logbook fails
                }
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting job data: ${e.message}", e)
            false
        }
    }

    /**
     * Export job data with a custom spreadsheet ID, including logbook entry.
     * This method is used for retry scenarios where the user provides a new spreadsheet ID.
     * The export is identical to the normal export except it uses the user-provided spreadsheet ID.
     *
     * @param customSpreadsheetId The new spreadsheet ID to use for export
     * @param job The job to export
     * @param pastures List of pastures with counter data
     * @param history Map of pasture names to their counter change history
     * @param surveyResponse Optional survey response for flight summary and logbook
     * @return Boolean indicating whether the export was successful
     */
    suspend fun exportJobDataWithCustomId(
        customSpreadsheetId: String,
        job: Job,
        pastures: List<Pasture>,
        history: Map<String, List<CounterChange>>,
        surveyResponse: SurveyResponse?,
        clientNotes: String = ""
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // Trim the spreadsheet ID to remove any leading/trailing whitespace
            val trimmedSpreadsheetId = customSpreadsheetId.trim()
            Log.d(TAG, "Starting export with custom ID for job: ${job.name} (ID: ${job.id})")
            Log.d(TAG, "Custom spreadsheet ID: '$trimmedSpreadsheetId'")
            Log.d(TAG, "Survey response provided: ${surveyResponse != null}")
            Log.d(TAG, "Client notes provided: '$clientNotes'")
            Log.d(TAG, "Client notes length: ${clientNotes.length}")

            val service = getSheetsService()
            val pilotName = job.pilotInfo?.name ?: "Unknown Pilot"

            // Create necessary sheets
            val spreadsheet = service.spreadsheets().get(trimmedSpreadsheetId).execute()
            val existingSheets = spreadsheet.sheets.map { it.properties.title }
            val sheetRequests = mutableListOf<Request>()

            // Create flight summary sheet if survey response is provided (named with pilot name)
            val flightSummarySheetName = "${pilotName} Flight Summary"
            if (surveyResponse != null) {
                if (!existingSheets.contains(flightSummarySheetName)) {
                    sheetRequests.add(
                        Request().setAddSheet(
                            AddSheetRequest().setProperties(
                                SheetProperties().setTitle(flightSummarySheetName)
                            )
                        )
                    )
                }
            }

            // Execute sheet creation requests
            if (sheetRequests.isNotEmpty()) {
                val batchRequest = BatchUpdateSpreadsheetRequest().setRequests(sheetRequests)
                executeWithRetry {
                    service.spreadsheets().batchUpdate(trimmedSpreadsheetId, batchRequest).execute()
                }
            }

            // Hide gridlines for all sheets
            val hideGridlinesRequests = mutableListOf<Request>()
            val updatedSpreadsheet = service.spreadsheets().get(trimmedSpreadsheetId).execute()
            updatedSpreadsheet.sheets.forEach { sheet ->
                hideGridlinesRequests.add(
                    Request().setUpdateSheetProperties(
                        UpdateSheetPropertiesRequest()
                            .setProperties(
                                SheetProperties()
                                    .setSheetId(sheet.properties.sheetId)
                                    .setGridProperties(
                                        GridProperties().setHideGridlines(true)
                                    )
                            )
                            .setFields("gridProperties.hideGridlines")
                    )
                )
            }

            // Execute gridline hiding requests
            if (hideGridlinesRequests.isNotEmpty()) {
                val gridlinesBatchRequest = BatchUpdateSpreadsheetRequest().setRequests(hideGridlinesRequests)
                executeWithRetry {
                    service.spreadsheets().batchUpdate(trimmedSpreadsheetId, gridlinesBatchRequest).execute()
                }
            }

            // Add timeline sheets for each pasture
            exportPastureTimelines(trimmedSpreadsheetId, pastures, history, service, job, pilotName)

            // Add total sheets for each pasture
            exportPastureTotals(trimmedSpreadsheetId, pastures, service, pilotName)

            // Export flight summary if survey response is provided (but skip logbook)
            if (surveyResponse != null) {
                try {
                    // Process flight summary inline (similar to original method but without logbook)
                    // Calculate flight duration using start and completion timestamps (rounded up to nearest minute)
                    val flightDurationMillis = surveyResponse.completionTimestamp - surveyResponse.startTimestamp
                    val flightDurationMinutes = (flightDurationMillis / (1000 * 60)).toDouble()
                    val flightDuration = kotlin.math.ceil(flightDurationMinutes).toInt()

                    // Format date and time in Central Time using completion timestamp
                    val centralTimeZone = TimeZone.getTimeZone("America/Chicago")
                    val centralDateFormat = SimpleDateFormat("MM/dd/yyyy", Locale.US).apply {
                        timeZone = centralTimeZone
                    }
                    val centralTimeFormat = SimpleDateFormat("hh:mm:ss a", Locale.US).apply {
                        timeZone = centralTimeZone
                    }
                    val completionDate = Date(surveyResponse.completionTimestamp)
                    val date = centralDateFormat.format(completionDate)
                    val time = centralTimeFormat.format(completionDate)

                    // Prepare the 18 fields in order
                    val labels = listOf(
                        "DATE", "TIME STARTED", "DURATION", "DAY TEMP.", "FLIGHT TEMP.", "DRONE HEIGHT   .",
                        "PILOT 1", "PILOT 2", "PILOT 3", "REVIEWER", "DRONE", "DRONE SPEED   .",
                        "CLIENT", "TOPOGRAPHY", "COVER", "ANIMAL DENSITY", "DAY WEATHER", "TRANSECT WIDTH   ."
                    )
                    val values = listOf(
                        date,
                        time,
                        "$flightDuration min",
                        "",
                        "",
                        "125 ft",
                        job.pilotInfo?.name ?: "",
                        "",
                        "",
                        "",
                        job.pilotInfo?.droneName ?: "DJI Matrice 4T",
                        "25 mph",
                        job.name, // property_name
                        surveyResponse.topography,
                        surveyResponse.cover,
                        surveyResponse.animalDensity,
                        surveyResponse.dayWeather,
                        "360 ft"
                    )

                    // Build rows: each row has B(label),C(data),D(label),E(data),F(label),G(data)
                    val summaryRows = (0 until 6).map { i ->
                        listOf(
                            "", // A (empty)
                            labels[i], values[i],
                            labels[i + 6], values[i + 6],
                            labels[i + 12], values[i + 12]
                        )
                    }

                    // Write to Flight Summary sheet (using pilot name)
                    val writeRange = "$flightSummarySheetName!A2:G7"
                    val valueRange = ValueRange().setValues(summaryRows)

                    executeWithRetry {
                        service.spreadsheets().values()
                            .update(trimmedSpreadsheetId, writeRange, valueRange)
                            .setValueInputOption("USER_ENTERED")
                            .execute()
                    }

                    // Apply formatting: add borders and bold text
                    val sheetId = getSheetId(service, trimmedSpreadsheetId, flightSummarySheetName)
                    val requests = mutableListOf<Request>()

                    // Bold all label columns (B, D, F)
                    requests.add(
                        Request().setRepeatCell(
                            RepeatCellRequest()
                                .setRange(GridRange()
                                    .setSheetId(sheetId)
                                    .setStartRowIndex(1)
                                    .setEndRowIndex(7)
                                    .setStartColumnIndex(1)
                                    .setEndColumnIndex(2))
                                .setCell(CellData().setUserEnteredFormat(
                                    CellFormat().setTextFormat(TextFormat().setBold(true))
                                ))
                                .setFields("userEnteredFormat.textFormat.bold")
                        )
                    )

                    requests.add(
                        Request().setRepeatCell(
                            RepeatCellRequest()
                                .setRange(GridRange()
                                    .setSheetId(sheetId)
                                    .setStartRowIndex(1)
                                    .setEndRowIndex(7)
                                    .setStartColumnIndex(3)
                                    .setEndColumnIndex(4))
                                .setCell(CellData().setUserEnteredFormat(
                                    CellFormat().setTextFormat(TextFormat().setBold(true))
                                ))
                                .setFields("userEnteredFormat.textFormat.bold")
                        )
                    )

                    requests.add(
                        Request().setRepeatCell(
                            RepeatCellRequest()
                                .setRange(GridRange()
                                    .setSheetId(sheetId)
                                    .setStartRowIndex(1)
                                    .setEndRowIndex(7)
                                    .setStartColumnIndex(5)
                                    .setEndColumnIndex(6))
                                .setCell(CellData().setUserEnteredFormat(
                                    CellFormat().setTextFormat(TextFormat().setBold(true))
                                ))
                                .setFields("userEnteredFormat.textFormat.bold")
                        )
                    )

                    // Add border to the entire table (B2:G7)
                    requests.add(
                        Request().setUpdateBorders(
                            UpdateBordersRequest()
                                .setRange(
                                    GridRange()
                                        .setSheetId(sheetId)
                                        .setStartRowIndex(1)
                                        .setEndRowIndex(7)
                                        .setStartColumnIndex(1)
                                        .setEndColumnIndex(7)
                                )
                                .setTop(Border().setStyle("SOLID"))
                                .setBottom(Border().setStyle("SOLID"))
                                .setLeft(Border().setStyle("SOLID"))
                                .setRight(Border().setStyle("SOLID"))
                                .setInnerHorizontal(Border().setStyle("SOLID"))
                                .setInnerVertical(Border().setStyle("SOLID"))
                        )
                    )
                    
                    // Auto-resize columns to fit content AFTER all formatting
                    requests.add(
                        Request().setAutoResizeDimensions(
                            AutoResizeDimensionsRequest()
                                .setDimensions(DimensionRange()
                                    .setSheetId(sheetId)
                                    .setDimension("COLUMNS")
                                    .setStartIndex(0)
                                    .setEndIndex(7))
                        )
                    )

                    val batchUpdateRequest = BatchUpdateSpreadsheetRequest().setRequests(requests)
                    executeWithRetry {
                        service.spreadsheets().batchUpdate(trimmedSpreadsheetId, batchUpdateRequest).execute()
                    }

                    // Append logbook entry (now included for custom ID exports)
                    try {
                        appendLogbookEntry(service, job, surveyResponse, clientNotes)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error appending logbook entry: "+e.message, e)
                        // Do not fail the export if logbook fails
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing survey response: "+e.message, e)
                    return@withContext false
                }
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting job data with custom ID: ${e.message}", e)
            false
        }
    }
}